# English translation for order_search_by_customer_po_fabric_panfu
# Copyright (C) 2025 panfu
# This file is distributed under the same license as the order_search_by_customer_po_fabric_panfu package.
# <AUTHOR> <EMAIL>, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: order_search_by_customer_po_fabric_panfu 17.0.1.0.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-29 15:20:00+0000\n"
"PO-Revision-Date: 2025-07-29 15:20:00+0000\n"
"Last-Translator: panfu <<EMAIL>>\n"
"Language-Team: English\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: order_search_by_customer_po_fabric_panfu
#: model_terms:ir.ui.view,arch_db:order_search_by_customer_po_fabric_panfu.portal_my_orders_search_form
msgid "Search by Order #, Customer PO, or Fabric #..."
msgstr "Search by Order #, Customer PO, or Fabric #..."

#. module: order_search_by_customer_po_fabric_panfu
#: model_terms:ir.ui.view,arch_db:order_search_by_customer_po_fabric_panfu.portal_my_orders_search_form
msgid "Search"
msgstr "Search"

#. module: order_search_by_customer_po_fabric_panfu
#: model_terms:ir.ui.view,arch_db:order_search_by_customer_po_fabric_panfu.portal_my_orders_search_form
msgid "Clear"
msgstr "Clear"
