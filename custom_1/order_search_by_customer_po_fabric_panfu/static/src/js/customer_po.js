odoo.define('order_search_by_customer_po_fabric.customer_po', function (require) {
    'use strict';

    var publicWidget = require('web.public.widget');
    var rpc = require('web.rpc');
    require('website_sale.website_sale');

    publicWidget.registry.CustomerPOWidget = publicWidget.Widget.extend({
        selector: '#o_cart_products',
        events: {
            'change .customer_po': '_onChangeCustomerPO',
        },

        _onChangeCustomerPO: function (ev) {
            var $input = $(ev.currentTarget);
            var customerPO = $input.val();
            var orderID = $('#cart_products').data('order-id');

            // Update the customer PO on the sale order
            rpc.query({
                model: 'sale.order',
                method: 'update_customer_po',
                args: [orderID, customerPO],
            }).then(function () {
                console.log('Customer PO updated successfully');
            }).guardedCatch(function (error) {
                console.error('Error updating Customer PO:', error);
            });
        },
    });

    return publicWidget.registry.CustomerPOWidget;
});