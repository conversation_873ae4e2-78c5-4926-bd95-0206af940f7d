from odoo import models, fields, api


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    customer_po = fields.Char()
    fabric_number = fields.Char()

    @api.model
    def update_customer_po(self, order_id, customer_po):
        """Update customer PO on the sale order"""
        order = self.browse(order_id)
        if order:
            order.customer_po = customer_po
            return True
        return False